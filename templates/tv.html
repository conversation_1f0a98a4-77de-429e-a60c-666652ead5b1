{% extends "base.html" %}

{% block title %}StreamFlix - TV Shows{% endblock %}

{% block content %}
<!-- TV Shows Header -->
<section class="content-section" style="padding-top: 2rem;">
    <div class="section-header">
        <h1 class="section-title" style="font-size: 2.5rem;">TV Shows</h1>
        <div class="view-all-btn" style="color: rgba(255,255,255,0.7);">
            {{ tv_shows.total_results if tv_shows else 0 }} TV shows available
        </div>
    </div>

    <!-- Genre Filter -->
    {% if genres and genres.genres %}
    <div class="genre-filter">
        <button class="genre-btn {% if not current_genre %}active{% endif %}" data-genre="">
            All Genres
        </button>
        {% for genre in genres.genres %}
        <button class="genre-btn {% if current_genre == genre.id|string %}active{% endif %}" data-genre="{{ genre.id }}">
            {{ genre.name }}
        </button>
        {% endfor %}
    </div>
    {% endif %}

    <!-- TV Shows Grid -->
    {% if tv_shows and tv_shows.results %}
    <div class="content-grid" id="tvShowsGrid">
        {% for show in tv_shows.results %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_tv', tv_id=show.id, season=1, episode=1) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                 alt="{{ show.name }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ show.name }}</h3>
                <div class="card-info">
                    <span>{{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(show.vote_average) if show.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Load More Button -->
    {% if tv_shows.page < tv_shows.total_pages %}
    <div class="load-more-container">
        <button class="load-more-btn" id="loadMoreBtn">
            Load More TV Shows
        </button>
    </div>
    {% endif %}

    {% else %}
    <div style="text-align: center; padding: 4rem 0; color: rgba(255,255,255,0.7);">
        <i class="fas fa-tv" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
        <h3>No TV shows found</h3>
        <p>Try adjusting your filters or check back later.</p>
    </div>
    {% endif %}
</section>
{% endblock %}

{% block scripts %}
<script>
// Initialize infinite scroll for TV shows page
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = {{ tv_shows.page if tv_shows else 1 }};
    let totalPages = {{ tv_shows.total_pages if tv_shows else 1 }};
    let isLoading = false;
    const currentGenre = '{{ current_genre or "" }}';

    // Load more functionality
    async function loadMoreTVShows() {
        if (isLoading || currentPage >= totalPages) return;

        isLoading = true;
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.disabled = true;
            loadMoreBtn.textContent = 'Loading...';
        }

        try {
            currentPage++;
            const response = await fetch(`/api/tv/load-more?page=${currentPage}&genre=${currentGenre}`);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                const tvShowsGrid = document.getElementById('tvShowsGrid');
                data.results.forEach(show => {
                    const showCard = createTVShowCard(show);
                    tvShowsGrid.appendChild(showCard);
                });

                totalPages = data.total_pages;
                
                // Update load more button
                if (currentPage >= totalPages) {
                    if (loadMoreBtn) loadMoreBtn.style.display = 'none';
                } else {
                    if (loadMoreBtn) {
                        loadMoreBtn.disabled = false;
                        loadMoreBtn.textContent = 'Load More TV Shows';
                    }
                }

                // Re-initialize animations for new cards
                initCardAnimations();
            }
        } catch (error) {
            console.error('Error loading more TV shows:', error);
            if (loadMoreBtn) {
                loadMoreBtn.disabled = false;
                loadMoreBtn.textContent = 'Load More TV Shows';
            }
        } finally {
            isLoading = false;
        }
    }

    // Create TV show card element
    function createTVShowCard(show) {
        const card = document.createElement('div');
        card.className = 'content-card';
        card.onclick = () => window.location.href = `/watch/tv/${show.id}/1/1`;
        
        const imageUrl = show.poster_path 
            ? `https://image.tmdb.org/t/p/w500${show.poster_path}`
            : '/static/images/placeholder.jpg';
        
        const year = show.first_air_date ? show.first_air_date.substring(0, 4) : 'N/A';
        const rating = show.vote_average ? show.vote_average.toFixed(1) : 'N/A';

        card.innerHTML = `
            <img src="${imageUrl}" 
                 alt="${show.name}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">${show.name}</h3>
                <div class="card-info">
                    <span>${year}</span>
                    <span>★ ${rating}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    // Attach load more event
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreTVShows);
    }

    // Auto-load on scroll
    window.addEventListener('scroll', function() {
        if (isLoading || currentPage >= totalPages) return;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;

        if (scrollPosition >= documentHeight - 1000) {
            loadMoreTVShows();
        }
    });
});
</script>
{% endblock %}
