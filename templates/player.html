{% extends "base.html" %}

{% block title %}
{% if content_type == 'movie' %}
StreamFlix - {{ content.title if content else 'Movie' }}
{% else %}
StreamFlix - {{ content.name if content else 'TV Show' }} S{{ season }}E{{ episode }}
{% endif %}
{% endblock %}

{% block content %}
<section class="content-section" style="padding-top: 1rem;">
    <!-- Player Container -->
    <div class="player-container">
        <iframe src="{{ embed_url }}" 
                class="player-iframe" 
                allowfullscreen 
                webkitallowfullscreen 
                mozallowfullscreen
                allow="autoplay; fullscreen; picture-in-picture">
        </iframe>
    </div>

    <!-- Content Information -->
    {% if content %}
    <div class="player-info">
        <div class="player-header">
            {% if content_type == 'movie' %}
            <h1 class="player-title">{{ content.title }}</h1>
            {% else %}
            <h1 class="player-title">{{ content.name }}</h1>
            <div class="episode-info">
                <span class="season-episode">Season {{ season }} • Episode {{ episode }}</span>
            </div>
            {% endif %}
        </div>

        <div class="player-meta">
            {% if content_type == 'movie' %}
                {% if content.release_date %}
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>{{ content.release_date[:4] }}</span>
                </div>
                {% endif %}
                {% if content.runtime %}
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ content.runtime }} min</span>
                </div>
                {% endif %}
            {% else %}
                {% if content.first_air_date %}
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>{{ content.first_air_date[:4] }}</span>
                </div>
                {% endif %}
                {% if content.number_of_seasons %}
                <div class="meta-item">
                    <i class="fas fa-tv"></i>
                    <span>{{ content.number_of_seasons }} Season{{ 's' if content.number_of_seasons != 1 else '' }}</span>
                </div>
                {% endif %}
            {% endif %}
            
            {% if content.vote_average %}
            <div class="meta-item">
                <i class="fas fa-star"></i>
                <span>{{ "%.1f"|format(content.vote_average) }}/10</span>
            </div>
            {% endif %}

            {% if content.genres %}
            <div class="meta-item">
                <i class="fas fa-tags"></i>
                <span>{{ content.genres[:3]|map(attribute='name')|join(', ') }}</span>
            </div>
            {% endif %}
        </div>

        {% if content.overview %}
        <div class="player-overview">
            <h3>Overview</h3>
            <p>{{ content.overview }}</p>
        </div>
        {% endif %}

        <!-- TV Show Season/Episode Navigation -->
        {% if content_type == 'tv' and content.seasons %}
        <div class="episode-navigation">
            <h3>Episodes</h3>
            <div class="season-selector">
                <label for="seasonSelect">Season:</label>
                <select id="seasonSelect" onchange="changeSeason()">
                    {% for season_data in content.seasons %}
                    {% if season_data.season_number > 0 %}
                    <option value="{{ season_data.season_number }}" {% if season_data.season_number == season %}selected{% endif %}>
                        Season {{ season_data.season_number }}
                    </option>
                    {% endif %}
                    {% endfor %}
                </select>
            </div>
            
            <div class="episode-grid" id="episodeGrid">
                <!-- Episodes will be loaded dynamically -->
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="player-actions">
            {% if content_type == 'movie' %}
            <a href="{{ url_for('movies') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Movies
            </a>
            {% else %}
            <a href="{{ url_for('tv_shows') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to TV Shows
            </a>
            {% endif %}
            
            <button class="btn btn-primary" onclick="addToWatchlist()">
                <i class="fas fa-plus"></i>
                Add to Watchlist
            </button>
            
            <button class="btn btn-secondary" onclick="shareContent()">
                <i class="fas fa-share"></i>
                Share
            </button>
        </div>
    </div>
    {% endif %}
</section>
{% endblock %}

{% block scripts %}
{% if content_type == 'tv' %}
<script>
const tvId = {{ content.id if content else 0 }};
const currentSeason = {{ season }};
const currentEpisode = {{ episode }};

// Change season functionality
function changeSeason() {
    const seasonSelect = document.getElementById('seasonSelect');
    const selectedSeason = seasonSelect.value;
    
    if (selectedSeason !== currentSeason.toString()) {
        window.location.href = `/watch/tv/${tvId}/${selectedSeason}/1`;
    }
}

// Load episodes for current season
async function loadEpisodes(seasonNumber) {
    try {
        const response = await fetch(`https://api.themoviedb.org/3/tv/${tvId}/season/${seasonNumber}?api_key=3308647fabe47a844ab269e6eab19132`);
        const seasonData = await response.json();
        
        const episodeGrid = document.getElementById('episodeGrid');
        if (seasonData.episodes && seasonData.episodes.length > 0) {
            episodeGrid.innerHTML = seasonData.episodes.map(episode => `
                <div class="episode-card ${episode.episode_number === currentEpisode ? 'active' : ''}" 
                     onclick="watchEpisode(${episode.episode_number})">
                    <div class="episode-number">E${episode.episode_number}</div>
                    <div class="episode-info">
                        <h4>${episode.name || 'Episode ' + episode.episode_number}</h4>
                        <p>${episode.overview ? episode.overview.substring(0, 100) + '...' : 'No description available'}</p>
                        <div class="episode-meta">
                            ${episode.air_date ? episode.air_date : ''}
                            ${episode.runtime ? ' • ' + episode.runtime + ' min' : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            episodeGrid.innerHTML = '<p>No episodes available for this season.</p>';
        }
    } catch (error) {
        console.error('Error loading episodes:', error);
        document.getElementById('episodeGrid').innerHTML = '<p>Error loading episodes.</p>';
    }
}

// Watch specific episode
function watchEpisode(episodeNumber) {
    if (episodeNumber !== currentEpisode) {
        window.location.href = `/watch/tv/${tvId}/${currentSeason}/${episodeNumber}`;
    }
}

// Load episodes on page load
document.addEventListener('DOMContentLoaded', function() {
    loadEpisodes(currentSeason);
});
</script>
{% endif %}

<script>
// Add to watchlist functionality
function addToWatchlist() {
    // This would typically save to a database or local storage
    alert('Added to watchlist! (This is a demo - implement your preferred storage method)');
}

// Share content functionality
function shareContent() {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Space bar to play/pause (if iframe supports it)
    if (e.code === 'Space' && e.target === document.body) {
        e.preventDefault();
        // Note: Due to iframe restrictions, we can't control the player directly
        // This would need to be implemented with a different player solution
    }
    
    // Escape to go back
    if (e.code === 'Escape') {
        {% if content_type == 'movie' %}
        window.location.href = '{{ url_for("movies") }}';
        {% else %}
        window.location.href = '{{ url_for("tv_shows") }}';
        {% endif %}
    }
});
</script>
{% endblock %}
