{% extends "base.html" %}

{% block title %}StreamFlix - Movies{% endblock %}

{% block content %}
<!-- Movies Header -->
<section class="content-section" style="padding-top: 2rem;">
    <div class="section-header">
        <h1 class="section-title" style="font-size: 2.5rem;">Movies</h1>
        <div class="view-all-btn" style="color: rgba(255,255,255,0.7);">
            {{ movies.total_results if movies else 0 }} movies available
        </div>
    </div>

    <!-- Genre Filter -->
    {% if genres and genres.genres %}
    <div class="genre-filter">
        <button class="genre-btn {% if not current_genre %}active{% endif %}" data-genre="">
            All Genres
        </button>
        {% for genre in genres.genres %}
        <button class="genre-btn {% if current_genre == genre.id|string %}active{% endif %}" data-genre="{{ genre.id }}">
            {{ genre.name }}
        </button>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Movies Grid -->
    {% if movies and movies.results %}
    <div class="content-grid" id="moviesGrid">
        {% for movie in movies.results %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_movie', movie_id=movie.id) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                 alt="{{ movie.title }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ movie.title }}</h3>
                <div class="card-info">
                    <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(movie.vote_average) if movie.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Load More Button -->
    {% if movies.page < movies.total_pages %}
    <div class="load-more-container">
        <button class="load-more-btn" id="loadMoreBtn">
            Load More Movies
        </button>
    </div>
    {% endif %}

    {% else %}
    <div style="text-align: center; padding: 4rem 0; color: rgba(255,255,255,0.7);">
        <i class="fas fa-film" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
        <h3>No movies found</h3>
        <p>Try adjusting your filters or check back later.</p>
    </div>
    {% endif %}
</section>
{% endblock %}

{% block scripts %}
<script>
// Initialize infinite scroll for movies page
document.addEventListener('DOMContentLoaded', function() {
    let currentPage = {{ movies.page if movies else 1 }};
    let totalPages = {{ movies.total_pages if movies else 1 }};
    let isLoading = false;
    const currentGenre = '{{ current_genre or "" }}';

    // Load more functionality
    async function loadMoreMovies() {
        if (isLoading || currentPage >= totalPages) return;

        isLoading = true;
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.disabled = true;
            loadMoreBtn.textContent = 'Loading...';
        }

        try {
            currentPage++;
            const response = await fetch(`/api/movies/load-more?page=${currentPage}&genre=${currentGenre}`);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                const moviesGrid = document.getElementById('moviesGrid');
                data.results.forEach(movie => {
                    const movieCard = createMovieCard(movie);
                    moviesGrid.appendChild(movieCard);
                });

                totalPages = data.total_pages;
                
                // Update load more button
                if (currentPage >= totalPages) {
                    if (loadMoreBtn) loadMoreBtn.style.display = 'none';
                } else {
                    if (loadMoreBtn) {
                        loadMoreBtn.disabled = false;
                        loadMoreBtn.textContent = 'Load More Movies';
                    }
                }

                // Re-initialize animations for new cards
                initCardAnimations();
            }
        } catch (error) {
            console.error('Error loading more movies:', error);
            if (loadMoreBtn) {
                loadMoreBtn.disabled = false;
                loadMoreBtn.textContent = 'Load More Movies';
            }
        } finally {
            isLoading = false;
        }
    }

    // Create movie card element
    function createMovieCard(movie) {
        const card = document.createElement('div');
        card.className = 'content-card';
        card.onclick = () => window.location.href = `/watch/movie/${movie.id}`;
        
        const imageUrl = movie.poster_path 
            ? `https://image.tmdb.org/t/p/w500${movie.poster_path}`
            : '/static/images/placeholder.jpg';
        
        const year = movie.release_date ? movie.release_date.substring(0, 4) : 'N/A';
        const rating = movie.vote_average ? movie.vote_average.toFixed(1) : 'N/A';

        card.innerHTML = `
            <img src="${imageUrl}" 
                 alt="${movie.title}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">${movie.title}</h3>
                <div class="card-info">
                    <span>${year}</span>
                    <span>★ ${rating}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    // Attach load more event
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreMovies);
    }

    // Auto-load on scroll
    window.addEventListener('scroll', function() {
        if (isLoading || currentPage >= totalPages) return;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;

        if (scrollPosition >= documentHeight - 1000) {
            loadMoreMovies();
        }
    });
});
</script>
{% endblock %}
