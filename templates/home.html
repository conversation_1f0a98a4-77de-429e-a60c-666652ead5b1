{% extends "base.html" %}

{% block title %}StreamFlix - Home{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    {% if trending_movies and trending_movies.results %}
    {% set hero_movie = trending_movies.results[0] %}
    <div class="hero-background" style="background-image: url('https://image.tmdb.org/t/p/original{{ hero_movie.backdrop_path }}');"></div>
    {% endif %}
    <div class="hero-overlay"></div>
    <div class="hero-content">
        {% if trending_movies and trending_movies.results %}
        <h1 class="hero-title">{{ hero_movie.title }}</h1>
        <p class="hero-description">{{ hero_movie.overview[:200] }}...</p>
        <div class="hero-buttons">
            <a href="{{ url_for('watch_movie', movie_id=hero_movie.id) }}" class="btn btn-primary">
                <i class="fas fa-play"></i>
                Watch Now
            </a>
            <a href="#" class="btn btn-secondary">
                <i class="fas fa-info-circle"></i>
                More Info
            </a>
        </div>
        {% else %}
        <h1 class="hero-title">Welcome to StreamFlix</h1>
        <p class="hero-description">Discover unlimited movies and TV shows with premium streaming quality.</p>
        <div class="hero-buttons">
            <a href="{{ url_for('movies') }}" class="btn btn-primary">
                <i class="fas fa-film"></i>
                Browse Movies
            </a>
            <a href="{{ url_for('tv_shows') }}" class="btn btn-secondary">
                <i class="fas fa-tv"></i>
                Browse TV Shows
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Trending Movies Section -->
{% if trending_movies and trending_movies.results %}
<section class="content-section">
    <div class="section-header">
        <h2 class="section-title">Trending Movies</h2>
        <a href="{{ url_for('movies') }}" class="view-all-btn">
            View All <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="content-grid">
        {% for movie in trending_movies.results[:12] %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_movie', movie_id=movie.id) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                 alt="{{ movie.title }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ movie.title }}</h3>
                <div class="card-info">
                    <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(movie.vote_average) if movie.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- Popular Movies Section -->
{% if popular_movies and popular_movies.results %}
<section class="content-section">
    <div class="section-header">
        <h2 class="section-title">Popular Movies</h2>
        <a href="{{ url_for('movies') }}" class="view-all-btn">
            View All <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="content-grid">
        {% for movie in popular_movies.results[:12] %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_movie', movie_id=movie.id) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                 alt="{{ movie.title }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ movie.title }}</h3>
                <div class="card-info">
                    <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(movie.vote_average) if movie.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- Trending TV Shows Section -->
{% if trending_tv and trending_tv.results %}
<section class="content-section">
    <div class="section-header">
        <h2 class="section-title">Trending TV Shows</h2>
        <a href="{{ url_for('tv_shows') }}" class="view-all-btn">
            View All <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="content-grid">
        {% for show in trending_tv.results[:12] %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_tv', tv_id=show.id, season=1, episode=1) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                 alt="{{ show.name }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ show.name }}</h3>
                <div class="card-info">
                    <span>{{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(show.vote_average) if show.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}

<!-- Popular TV Shows Section -->
{% if popular_tv and popular_tv.results %}
<section class="content-section">
    <div class="section-header">
        <h2 class="section-title">Popular TV Shows</h2>
        <a href="{{ url_for('tv_shows') }}" class="view-all-btn">
            View All <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="content-grid">
        {% for show in popular_tv.results[:12] %}
        <div class="content-card" onclick="window.location.href='{{ url_for('watch_tv', tv_id=show.id, season=1, episode=1) }}'">
            <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                 alt="{{ show.name }}" 
                 class="card-image"
                 onerror="this.src='/static/images/placeholder.jpg'">
            <div class="card-overlay">
                <h3 class="card-title">{{ show.name }}</h3>
                <div class="card-info">
                    <span>{{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}</span>
                    <span>★ {{ "%.1f"|format(show.vote_average) if show.vote_average else 'N/A' }}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn" title="Play">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="card-btn" title="Add to Watchlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="card-btn" title="More Info">
                        <i class="fas fa-info"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endif %}
{% endblock %}
