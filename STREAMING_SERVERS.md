# StreamFlix - Available Streaming Servers

## 🎬 Integrated Streaming APIs

### 1. **AutoEmbed** (Default)
- **URL Format:** 
  - Movies: `https://player.autoembed.cc/embed/movie/{id}`
  - TV Shows: `https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}`
- **Description:** Fast and reliable streaming
- **Features:** Your original API with good performance

### 2. **MultiEmbed**
- **URL Format:**
  - Movies: `https://multiembed.mov/?video_id={id}&tmdb=1`
  - TV Shows: `https://multiembed.mov/directstream.php?video_id={id}&tmdb=1&s={season}&e={episode}`
- **Description:** High quality streams
- **Features:** TMDB integration, multiple quality options

### 3. **VidSrc**
- **URL Format:**
  - Movies: `https://vidsrc.to/embed/movie/{id}`
  - TV Shows: `https://vidsrc.to/embed/tv/{id}/{season}/{episode}`
- **Description:** Multiple server options
- **Features:** Popular streaming API with good reliability

### 4. **VidSrc.me**
- **URL Format:**
  - Movies: `https://vidsrc.me/embed/movie?tmdb={id}`
  - TV Shows: `https://vidsrc.me/embed/tv?tmdb={id}&season={season}&episode={episode}`
- **Description:** Alternative streaming source
- **Features:** TMDB-based streaming with good coverage

### 5. **SuperEmbed**
- **URL Format:**
  - Movies: `https://multiembed.mov/directstream.php?video_id={id}&tmdb=1`
  - TV Shows: `https://multiembed.mov/directstream.php?video_id={id}&tmdb=1&s={season}&e={episode}`
- **Description:** Premium streaming experience
- **Features:** High-quality streams with minimal ads

## 🎯 How Server Selection Works

1. **Default Server:** AutoEmbed is set as the default streaming server
2. **Server Switching:** Users can switch between servers on the player page
3. **URL Structure:** Each server has its own URL pattern in the routes
4. **Fallback:** If a server fails, users can easily switch to another
5. **Responsive Design:** Server selection works on all devices

## 🔧 Technical Implementation

- **Backend:** Flask routes handle server selection with URL parameters
- **Frontend:** JavaScript handles seamless server switching without page reload
- **CSS:** Beautiful server selection interface with hover effects
- **API Integration:** All servers use TMDB IDs for content identification

## 📱 User Experience

- **Easy Selection:** Click any server button to switch instantly
- **Visual Feedback:** Active server is highlighted with red accent
- **Loading States:** Smooth transitions with loading indicators
- **Mobile Friendly:** Responsive design works on all screen sizes

## 🚀 Usage Examples

### Movies:
- `/watch/movie/123456` (default AutoEmbed)
- `/watch/movie/123456/vidsrc` (VidSrc server)
- `/watch/movie/123456/multiembed` (MultiEmbed server)

### TV Shows:
- `/watch/tv/123456/1/1` (default AutoEmbed)
- `/watch/tv/123456/1/1/vidsrc` (VidSrc server)
- `/watch/tv/123456/1/1/superembed` (SuperEmbed server)
