// Main JavaScript for StreamFlix

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavbar();
    initSearch();
    initInfiniteScroll();
    initGenreFilter();
    initCardAnimations();
    initPageTransitions();
    initSmoothScroll();
});

// Navbar scroll effect
function initNavbar() {
    const navbar = document.getElementById('navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// Search functionality
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout;

    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
}

async function performSearch(query) {
    const searchResults = document.getElementById('searchResults');
    
    try {
        showLoading();
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        
        displaySearchResults(data.results || []);
        hideLoading();
    } catch (error) {
        console.error('Search error:', error);
        hideLoading();
    }
}

function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    
    if (results.length === 0) {
        searchResults.innerHTML = '<div style="padding: 1rem; text-align: center; color: rgba(255,255,255,0.7);">No results found</div>';
    } else {
        searchResults.innerHTML = results.slice(0, 8).map(item => {
            const imageUrl = item.poster_path 
                ? `https://image.tmdb.org/t/p/w200${item.poster_path}`
                : '/static/images/placeholder.jpg';
            
            const mediaType = item.media_type || 'movie';
            const title = item.title || item.name;
            const year = item.release_date || item.first_air_date;
            const yearText = year ? new Date(year).getFullYear() : '';
            
            return `
                <div class="search-result-item" onclick="navigateToContent('${mediaType}', ${item.id})">
                    <img src="${imageUrl}" alt="${title}" class="search-result-image" onerror="this.src='/static/images/placeholder.jpg'">
                    <div class="search-result-info">
                        <h4>${title}</h4>
                        <p>${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} ${yearText ? '• ' + yearText : ''}</p>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    searchResults.style.display = 'block';
}

function navigateToContent(mediaType, id) {
    if (mediaType === 'movie') {
        window.location.href = `/watch/movie/${id}`;
    } else if (mediaType === 'tv') {
        window.location.href = `/watch/tv/${id}/1/1`;
    }
}

// Infinite scroll functionality
function initInfiniteScroll() {
    let currentPage = 1;
    let isLoading = false;
    let hasMoreContent = true;

    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreContent);
    }

    // Auto-load on scroll
    window.addEventListener('scroll', function() {
        if (isLoading || !hasMoreContent) return;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;

        if (scrollPosition >= documentHeight - 1000) {
            loadMoreContent();
        }
    });

    async function loadMoreContent() {
        if (isLoading || !hasMoreContent) return;

        isLoading = true;
        showLoading();

        try {
            currentPage++;
            const currentPath = window.location.pathname;
            const urlParams = new URLSearchParams(window.location.search);
            const genre = urlParams.get('genre') || '';

            let apiUrl;
            if (currentPath.includes('/movies')) {
                apiUrl = `/api/movies/load-more?page=${currentPage}&genre=${genre}`;
            } else if (currentPath.includes('/tv')) {
                apiUrl = `/api/tv/load-more?page=${currentPage}&genre=${genre}`;
            } else {
                return;
            }

            const response = await fetch(apiUrl);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                appendContentToGrid(data.results);
                hasMoreContent = currentPage < data.total_pages;
            } else {
                hasMoreContent = false;
            }

            updateLoadMoreButton();
        } catch (error) {
            console.error('Error loading more content:', error);
        } finally {
            isLoading = false;
            hideLoading();
        }
    }

    function appendContentToGrid(items) {
        const contentGrid = document.querySelector('.content-grid');
        if (!contentGrid) return;

        items.forEach(item => {
            const card = createContentCard(item);
            contentGrid.appendChild(card);
        });

        // Re-initialize animations for new cards
        initCardAnimations();
    }

    function updateLoadMoreButton() {
        const loadMoreBtn = document.querySelector('.load-more-btn');
        if (loadMoreBtn) {
            if (hasMoreContent) {
                loadMoreBtn.style.display = 'block';
                loadMoreBtn.disabled = false;
                loadMoreBtn.textContent = 'Load More';
            } else {
                loadMoreBtn.style.display = 'none';
            }
        }
    }
}

function createContentCard(item) {
    const card = document.createElement('div');
    card.className = 'content-card';
    
    const imageUrl = item.poster_path 
        ? `https://image.tmdb.org/t/p/w500${item.poster_path}`
        : '/static/images/placeholder.jpg';
    
    const title = item.title || item.name;
    const year = item.release_date || item.first_air_date;
    const yearText = year ? new Date(year).getFullYear() : '';
    const rating = item.vote_average ? item.vote_average.toFixed(1) : 'N/A';
    
    const mediaType = item.title ? 'movie' : 'tv';
    const watchUrl = mediaType === 'movie' 
        ? `/watch/movie/${item.id}`
        : `/watch/tv/${item.id}/1/1`;

    card.innerHTML = `
        <img src="${imageUrl}" alt="${title}" class="card-image" onerror="this.src='/static/images/placeholder.jpg'">
        <div class="card-overlay">
            <h3 class="card-title">${title}</h3>
            <div class="card-info">
                <span>${yearText}</span>
                <span>★ ${rating}</span>
            </div>
            <div class="card-actions">
                <button class="card-btn" onclick="window.location.href='${watchUrl}'" title="Play">
                    <i class="fas fa-play"></i>
                </button>
                <button class="card-btn" title="Add to Watchlist">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="card-btn" title="More Info">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        </div>
    `;

    return card;
}

// Genre filter functionality
function initGenreFilter() {
    const genreButtons = document.querySelectorAll('.genre-btn');
    
    genreButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const genre = this.dataset.genre;
            
            // Update active state
            genreButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Update URL and reload content
            const url = new URL(window.location);
            if (genre) {
                url.searchParams.set('genre', genre);
            } else {
                url.searchParams.delete('genre');
            }
            
            window.location.href = url.toString();
        });
    });
}

// Card animations
function initCardAnimations() {
    const cards = document.querySelectorAll('.content-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-fade-in-up');
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = `opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)`;

        observer.observe(card);
    });
}

// Enhanced page transitions
function initPageTransitions() {
    // Animate sections on scroll
    const sections = document.querySelectorAll('.content-section');

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '100px'
    });

    sections.forEach(section => {
        sectionObserver.observe(section);
    });

    // Animate hero content
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        setTimeout(() => {
            heroContent.classList.add('animate-fade-in-left');
        }, 300);
    }

    // Animate navigation
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.classList.add('animate-slide-in-top');
    }
}

// Smooth scroll for anchor links
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Loading animations
function showLoadingWithAnimation() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'flex';
        spinner.style.opacity = '0';
        setTimeout(() => {
            spinner.style.opacity = '1';
        }, 10);
    }
}

function hideLoadingWithAnimation() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.opacity = '0';
        setTimeout(() => {
            spinner.style.display = 'none';
        }, 300);
    }
}

// Loading spinner functions (updated with animations)
function showLoading() {
    showLoadingWithAnimation();
}

function hideLoading() {
    hideLoadingWithAnimation();
}

// Utility functions
function formatRuntime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}
