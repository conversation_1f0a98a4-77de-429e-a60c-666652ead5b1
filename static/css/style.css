/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #0f0f0f;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Responsive Images */
img {
    max-width: 100%;
    height: auto;
}

/* Responsive Videos */
video {
    max-width: 100%;
    height: auto;
}

/* Responsive Containers */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Responsive Text */
.text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

/* Hide elements on mobile */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

/* Responsive Utilities */
.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

/* Touch-friendly elements */
@media (hover: none) and (pointer: coarse) {
    .content-card {
        transform: none;
    }

    .content-card:hover {
        transform: none;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .btn {
        min-height: 44px;
        min-width: 44px;
    }

    .card-btn {
        min-height: 40px;
        min-width: 40px;
        padding: 0.6rem;
    }

    .genre-btn {
        min-height: 40px;
        padding: 0.6rem 1rem;
    }

    .server-btn {
        min-height: 60px;
    }
}

/* Improved focus states for accessibility */
.btn:focus,
.card-btn:focus,
.genre-btn:focus,
.server-btn:focus,
.nav-link:focus,
.mobile-nav-link:focus {
    outline: 2px solid #e50914;
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Prevent horizontal scroll */
body, html {
    overflow-x: hidden;
}

/* Better text rendering */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for content */
.player-overview,
.card-title,
.hero-description,
.episode-info p {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.7) 50%, transparent 100%);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #e50914;
    font-size: 1.8rem;
    font-weight: 700;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: #e50914;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: #e50914;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.3s ease;
}

.mobile-menu-btn:hover {
    color: #e50914;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-menu-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    color: #ffffff;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.mobile-nav-link:hover {
    background: rgba(229, 9, 20, 0.1);
    color: #e50914;
    transform: translateX(5px);
}

.mobile-nav-link i {
    font-size: 1.2rem;
    width: 20px;
}

/* Search Container */
.search-container {
    position: relative;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.7rem 1rem 0.7rem 2.5rem;
    color: #ffffff;
    width: 300px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.search-input:focus {
    outline: none;
    border-color: #e50914;
    background: rgba(255, 255, 255, 0.15);
    width: 350px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 10px;
    margin-top: 0.5rem;
    max-height: 400px;
    overflow-y: auto;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}



/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 70vh;
    background: linear-gradient(45deg, #0f0f0f, #1a1a1a);
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.8) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, #e50914);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #e50914, #b20710);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(229, 9, 20, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Content Sections */
.content-section {
    padding: 3rem 0;
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 2rem;
    padding-right: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
}

.view-all-btn {
    color: #e50914;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-all-btn:hover {
    color: #ffffff;
    transform: translateX(5px);
}

/* Movie/TV Cards Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.content-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
}

.content-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.card-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
    background: linear-gradient(135deg, #333, #555);
}

.card-image[src*="placeholder.jpg"],
.card-image[src=""],
.card-image:not([src]) {
    background: linear-gradient(135deg, #333, #555);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
}

.card-image[src*="placeholder.jpg"]::after,
.card-image[src=""]::after,
.card-image:not([src])::after {
    content: "No Image\AAvailable";
    white-space: pre;
}

.content-card:hover .card-image {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 2rem 1rem 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.content-card:hover .card-overlay {
    transform: translateY(0);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.card-info {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.card-btn:hover {
    background: #e50914;
    transform: scale(1.1);
}



@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(229, 9, 20, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.content-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.content-card:hover::before {
    opacity: 1;
}

.content-card:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6), 0 0 30px rgba(229, 9, 20, 0.2);
}

/* Enhanced Button Animations */
.btn {
    position: relative;
    overflow: hidden;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(229, 9, 20, 0.5);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.1);
}

/* Footer */
.footer {
    background: #0a0a0a;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h4 {
    color: #e50914;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #e50914;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
}

/* Infinite Scroll */
.load-more-container {
    text-align: center;
    padding: 2rem 0;
}

.load-more-btn {
    background: linear-gradient(135deg, #e50914, #b20710);
    color: #ffffff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(229, 9, 20, 0.4);
}

.load-more-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Genre Filter */
.genre-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.genre-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: #ffffff;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.genre-btn:hover,
.genre-btn.active {
    background: #e50914;
    border-color: #e50914;
    transform: translateY(-2px);
}

/* Server Selection */
.server-selection {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.server-selection h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.server-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.server-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.server-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(229, 9, 20, 0.5);
    transform: translateY(-2px);
}

.server-btn.active {
    background: linear-gradient(135deg, rgba(229, 9, 20, 0.2), rgba(229, 9, 20, 0.1));
    border-color: #e50914;
    box-shadow: 0 0 20px rgba(229, 9, 20, 0.3);
}

.server-name {
    color: #ffffff;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.server-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Player Styles */
.player-container {
    position: relative;
    width: 100%;
    height: 70vh;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.player-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.player-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.loading-text {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 500;
}

.player-info {
    padding: 2rem;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
    border-radius: 12px;
}

.player-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
}

.player-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.player-overview {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

/* Search Results */
.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.search-result-image {
    width: 60px;
    height: 90px;
    object-fit: cover;
    border-radius: 6px;
}

.search-result-info h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.search-result-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Episode Navigation */
.episode-navigation {
    margin-top: 2rem;
}

.episode-navigation h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.season-selector {
    margin-bottom: 1.5rem;
}

.season-selector label {
    color: rgba(255, 255, 255, 0.8);
    margin-right: 1rem;
    font-weight: 500;
}

.season-selector select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
}

.season-selector select:focus {
    outline: none;
    border-color: #e50914;
}

.episode-grid {
    display: grid;
    gap: 1rem;
}

.episode-card {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.episode-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.episode-card.active {
    border-color: #e50914;
    background: rgba(229, 9, 20, 0.1);
}

.episode-number {
    background: #e50914;
    color: #ffffff;
    padding: 0.5rem;
    border-radius: 8px;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
    height: fit-content;
}

.episode-info h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.episode-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.episode-meta {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
}

.player-header {
    margin-bottom: 1.5rem;
}

.episode-info .season-episode {
    color: #e50914;
    font-size: 1rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: block;
}

.player-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Responsive Design */

/* Extra Large Screens (1400px and up) */
@media (min-width: 1400px) {
    .nav-container,
    .content-section {
        max-width: 1600px;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .hero-title {
        font-size: 4rem;
    }

    .hero-description {
        font-size: 1.3rem;
    }
}

/* Large Screens (1200px to 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .hero-title {
        font-size: 3.5rem;
    }
}

/* Medium Large Screens (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .nav-container {
        padding: 0 1.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1.2rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .server-buttons {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Medium Screens (768px to 991px) - Tablets */
@media (max-width: 991px) and (min-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-left {
        gap: 1.5rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .search-input {
        width: 180px;
    }

    .search-input:focus {
        width: 220px;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1rem;
    }

    .server-buttons {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1rem;
    }

    .player-container {
        height: 60vh;
    }
}

/* Small Screens (576px to 767px) - Large Mobile */
@media (max-width: 767px) and (min-width: 576px) {
    .hide-mobile {
        display: none !important;
    }

    .show-mobile {
        display: block !important;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-left {
        gap: 1rem;
    }

    .nav-menu {
        display: none;
    }

    .search-input {
        width: 160px;
        font-size: 0.9rem;
    }

    .search-input:focus {
        width: 200px;
    }

    .hero-title {
        font-size: 2.2rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.8rem;
    }

    .content-section {
        padding: 2rem 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .server-buttons {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .server-btn {
        padding: 1rem;
    }

    .player-container {
        height: 50vh;
    }

    .genre-filter {
        gap: 0.5rem;
    }

    .genre-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }
}

/* Extra Small Screens (up to 575px) - Mobile */
@media (max-width: 575px) {
    .hide-mobile {
        display: none !important;
    }

    .show-mobile {
        display: block !important;
    }

    .navbar {
        padding: 0.3rem 0;
    }

    .nav-container {
        padding: 0 0.8rem;
        flex-wrap: wrap;
    }

    .nav-left {
        gap: 0.5rem;
        flex: 1;
    }

    .logo {
        font-size: 1.4rem;
    }

    .nav-menu {
        display: none;
    }

    .nav-right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
    }

    .search-container {
        width: 100%;
        margin-top: 0.5rem;
        order: 3;
    }

    .search-input {
        width: 100%;
        font-size: 0.9rem;
        padding: 0.6rem 1rem 0.6rem 2.2rem;
    }

    .search-input:focus {
        width: 100%;
    }

    .hero-section {
        height: 50vh;
        padding: 1rem 0;
    }

    .hero-title {
        font-size: 1.8rem;
        line-height: 1.1;
        margin-bottom: 0.8rem;
    }

    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 0.6rem;
        width: 100%;
    }

    .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        width: 100%;
        justify-content: center;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.6rem;
    }

    .content-section {
        padding: 1.5rem 0.8rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .view-all-btn {
        font-size: 0.9rem;
    }

    .card-image {
        height: 180px;
    }

    .card-title {
        font-size: 0.9rem;
    }

    .card-info {
        font-size: 0.8rem;
    }

    .card-actions {
        gap: 0.3rem;
    }

    .card-btn {
        padding: 0.4rem;
        font-size: 0.8rem;
    }

    .server-selection {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .server-selection h3 {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }

    .server-buttons {
        grid-template-columns: 1fr;
        gap: 0.6rem;
    }

    .server-btn {
        padding: 0.8rem;
    }

    .server-name {
        font-size: 0.85rem;
    }

    .server-desc {
        font-size: 0.75rem;
    }

    .player-container {
        height: 40vh;
        margin-bottom: 1rem;
    }

    .player-info {
        padding: 1rem;
    }

    .player-title {
        font-size: 1.5rem;
        line-height: 1.2;
    }

    .player-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .meta-item {
        font-size: 0.8rem;
    }

    .player-overview {
        font-size: 0.9rem;
    }

    .player-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .genre-filter {
        gap: 0.4rem;
        margin-bottom: 1.5rem;
    }

    .genre-btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
    }

    .episode-grid {
        gap: 0.8rem;
    }

    .episode-card {
        flex-direction: column;
        gap: 0.8rem;
        padding: 0.8rem;
    }

    .episode-number {
        align-self: flex-start;
        min-width: auto;
        padding: 0.4rem 0.6rem;
    }

    .footer-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .footer {
        padding: 2rem 0 1rem;
    }
}

/* Ultra Small Screens (up to 320px) - Very Small Mobile */
@media (max-width: 320px) {
    .logo {
        font-size: 1.2rem;
    }

    .hero-title {
        font-size: 1.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }

    .card-image {
        height: 150px;
    }

    .player-container {
        height: 35vh;
    }

    .server-btn {
        padding: 0.6rem;
    }
}
