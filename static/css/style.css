/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #0f0f0f;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.7) 50%, transparent 100%);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #e50914;
    font-size: 1.8rem;
    font-weight: 700;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: #e50914;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: #e50914;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Search Container */
.search-container {
    position: relative;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.7rem 1rem 0.7rem 2.5rem;
    color: #ffffff;
    width: 300px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.search-input:focus {
    outline: none;
    border-color: #e50914;
    background: rgba(255, 255, 255, 0.15);
    width: 350px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 10px;
    margin-top: 0.5rem;
    max-height: 400px;
    overflow-y: auto;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-menu {
    font-size: 1.5rem;
    color: #ffffff;
    cursor: pointer;
    transition: color 0.3s ease;
}

.user-menu:hover {
    color: #e50914;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 70vh;
    background: linear-gradient(45deg, #0f0f0f, #1a1a1a);
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.8) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, #e50914);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #e50914, #b20710);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(229, 9, 20, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Content Sections */
.content-section {
    padding: 3rem 0;
    max-width: 1400px;
    margin: 0 auto;
    padding-left: 2rem;
    padding-right: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
}

.view-all-btn {
    color: #e50914;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-all-btn:hover {
    color: #ffffff;
    transform: translateX(5px);
}

/* Movie/TV Cards Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.content-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
}

.content-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.card-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
    background: linear-gradient(135deg, #333, #555);
}

.card-image[src*="placeholder.jpg"],
.card-image[src=""],
.card-image:not([src]) {
    background: linear-gradient(135deg, #333, #555);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
}

.card-image[src*="placeholder.jpg"]::after,
.card-image[src=""]::after,
.card-image:not([src])::after {
    content: "No Image\AAvailable";
    white-space: pre;
}

.content-card:hover .card-image {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 2rem 1rem 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.content-card:hover .card-overlay {
    transform: translateY(0);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.card-info {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.card-btn:hover {
    background: #e50914;
    transform: scale(1.1);
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #e50914;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Additional Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(229, 9, 20, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(229, 9, 20, 0.5);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.content-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(229, 9, 20, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.content-card:hover::before {
    opacity: 1;
}

.content-card:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6), 0 0 30px rgba(229, 9, 20, 0.2);
}

/* Enhanced Button Animations */
.btn {
    position: relative;
    overflow: hidden;
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(229, 9, 20, 0.5);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.1);
}

/* Footer */
.footer {
    background: #0a0a0a;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h4 {
    color: #e50914;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #e50914;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
}

/* Infinite Scroll */
.load-more-container {
    text-align: center;
    padding: 2rem 0;
}

.load-more-btn {
    background: linear-gradient(135deg, #e50914, #b20710);
    color: #ffffff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(229, 9, 20, 0.4);
}

.load-more-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Genre Filter */
.genre-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.genre-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    color: #ffffff;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.genre-btn:hover,
.genre-btn.active {
    background: #e50914;
    border-color: #e50914;
    transform: translateY(-2px);
}

/* Player Styles */
.player-container {
    position: relative;
    width: 100%;
    height: 70vh;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.player-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.player-info {
    padding: 2rem;
    background: linear-gradient(145deg, #1a1a1a, #0f0f0f);
    border-radius: 12px;
}

.player-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
}

.player-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.player-overview {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

/* Search Results */
.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.search-result-image {
    width: 60px;
    height: 90px;
    object-fit: cover;
    border-radius: 6px;
}

.search-result-info h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.search-result-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Episode Navigation */
.episode-navigation {
    margin-top: 2rem;
}

.episode-navigation h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.season-selector {
    margin-bottom: 1.5rem;
}

.season-selector label {
    color: rgba(255, 255, 255, 0.8);
    margin-right: 1rem;
    font-weight: 500;
}

.season-selector select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
}

.season-selector select:focus {
    outline: none;
    border-color: #e50914;
}

.episode-grid {
    display: grid;
    gap: 1rem;
}

.episode-card {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.episode-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.episode-card.active {
    border-color: #e50914;
    background: rgba(229, 9, 20, 0.1);
}

.episode-number {
    background: #e50914;
    color: #ffffff;
    padding: 0.5rem;
    border-radius: 8px;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
    height: fit-content;
}

.episode-info h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.episode-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.episode-meta {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
}

.player-header {
    margin-bottom: 1.5rem;
}

.episode-info .season-episode {
    color: #e50914;
    font-size: 1rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: block;
}

.player-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-left {
        gap: 1rem;
    }

    .nav-menu {
        display: none;
    }

    .search-input {
        width: 200px;
    }

    .search-input:focus {
        width: 250px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }

    .content-section {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .player-container {
        height: 50vh;
    }

    .genre-filter {
        gap: 0.5rem;
    }

    .genre-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}
