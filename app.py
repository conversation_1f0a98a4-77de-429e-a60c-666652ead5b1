from flask import Flask, render_template, request, jsonify
import requests
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)

# TMDB API configuration
TMDB_API_KEY = "3308647fabe47a844ab269e6eab19132"
TMDB_BASE_URL = "https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL = "https://image.tmdb.org/t/p/w500"

# Streaming Server Configurations
STREAMING_SERVERS = {
    'autoembed': {
        'name': 'AutoEmbed',
        'movie_url': 'https://player.autoembed.cc/embed/movie/{id}',
        'tv_url': 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
        'description': 'Fast and reliable streaming'
    },
    'multiembed': {
        'name': 'MultiEmbed',
        'movie_url': 'https://multiembed.mov/?video_id={id}&tmdb=1',
        'tv_url': 'https://multiembed.mov/directstream.php?video_id={id}&tmdb=1&s={season}&e={episode}',
        'description': 'High quality streams'
    },
    'vidsrc': {
        'name': 'VidSrc',
        'movie_url': 'https://vidsrc.to/embed/movie/{id}',
        'tv_url': 'https://vidsrc.to/embed/tv/{id}/{season}/{episode}',
        'description': 'Multiple server options'
    },
    'vidsrc_me': {
        'name': 'VidSrc.me',
        'movie_url': 'https://vidsrc.me/embed/movie?tmdb={id}',
        'tv_url': 'https://vidsrc.me/embed/tv?tmdb={id}&season={season}&episode={episode}',
        'description': 'Alternative streaming source'
    },
    'superembed': {
        'name': 'SuperEmbed',
        'movie_url': 'https://multiembed.mov/directstream.php?video_id={id}&tmdb=1',
        'tv_url': 'https://multiembed.mov/directstream.php?video_id={id}&tmdb=1&s={season}&e={episode}',
        'description': 'Premium streaming experience'
    }
}

def get_tmdb_data(endpoint, params=None):
    """Helper function to fetch data from TMDB API"""
    if params is None:
        params = {}
    params['api_key'] = TMDB_API_KEY
    
    try:
        response = requests.get(f"{TMDB_BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error fetching TMDB data: {e}")
        return None

@app.route('/')
def home():
    """Home page with trending movies and TV shows"""
    # Get trending movies and TV shows
    trending_movies = get_tmdb_data('trending/movie/week')
    trending_tv = get_tmdb_data('trending/tv/week')
    popular_movies = get_tmdb_data('movie/popular')
    popular_tv = get_tmdb_data('tv/popular')
    
    return render_template('home.html', 
                         trending_movies=trending_movies,
                         trending_tv=trending_tv,
                         popular_movies=popular_movies,
                         popular_tv=popular_tv)

@app.route('/movies')
def movies():
    """Movies page with categories"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    movies_data = get_tmdb_data('discover/movie', params)
    genres = get_tmdb_data('genre/movie/list')
    
    return render_template('movies.html', 
                         movies=movies_data,
                         genres=genres,
                         current_genre=genre)

@app.route('/tv')
def tv_shows():
    """TV shows page"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    tv_data = get_tmdb_data('discover/tv', params)
    genres = get_tmdb_data('genre/tv/list')
    
    return render_template('tv.html', 
                         tv_shows=tv_data,
                         genres=genres,
                         current_genre=genre)

@app.route('/watch/movie/<int:movie_id>')
@app.route('/watch/movie/<int:movie_id>/<server>')
def watch_movie(movie_id, server='autoembed'):
    """Movie player page"""
    movie_details = get_tmdb_data(f'movie/{movie_id}')

    # Get server configuration
    server_config = STREAMING_SERVERS.get(server, STREAMING_SERVERS['autoembed'])
    embed_url = server_config['movie_url'].format(id=movie_id)

    return render_template('player.html',
                         content=movie_details,
                         embed_url=embed_url,
                         content_type='movie',
                         current_server=server,
                         servers=STREAMING_SERVERS,
                         movie_id=movie_id)

@app.route('/watch/tv/<int:tv_id>/<int:season>/<int:episode>')
@app.route('/watch/tv/<int:tv_id>/<int:season>/<int:episode>/<server>')
def watch_tv(tv_id, season, episode, server='autoembed'):
    """TV show player page"""
    tv_details = get_tmdb_data(f'tv/{tv_id}')

    # Get server configuration
    server_config = STREAMING_SERVERS.get(server, STREAMING_SERVERS['autoembed'])
    embed_url = server_config['tv_url'].format(id=tv_id, season=season, episode=episode)

    return render_template('player.html',
                         content=tv_details,
                         embed_url=embed_url,
                         content_type='tv',
                         season=season,
                         episode=episode,
                         current_server=server,
                         servers=STREAMING_SERVERS,
                         tv_id=tv_id)

@app.route('/api/search')
def search():
    """Search API endpoint"""
    query = request.args.get('q', '')
    if not query:
        return jsonify({'results': []})
    
    search_results = get_tmdb_data('search/multi', {'query': query})
    return jsonify(search_results)

@app.route('/api/movies/load-more')
def load_more_movies():
    """Load more movies for infinite scroll"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    movies_data = get_tmdb_data('discover/movie', params)
    return jsonify(movies_data)

@app.route('/api/tv/load-more')
def load_more_tv():
    """Load more TV shows for infinite scroll"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    tv_data = get_tmdb_data('discover/tv', params)
    return jsonify(tv_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
