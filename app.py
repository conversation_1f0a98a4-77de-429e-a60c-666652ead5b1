from flask import Flask, render_template, request, jsonify
import requests
import os
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-here')

# TMDB API configuration
TMDB_API_KEY = "3308647fabe47a844ab269e6eab19132"
TMDB_BASE_URL = "https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL = "https://image.tmdb.org/t/p/w500"

# Autoembed API configuration
AUTOEMBED_BASE_URL = "https://player.autoembed.cc/embed"

def get_tmdb_data(endpoint, params=None):
    """Helper function to fetch data from TMDB API"""
    if params is None:
        params = {}
    params['api_key'] = TMDB_API_KEY
    
    try:
        response = requests.get(f"{TMDB_BASE_URL}/{endpoint}", params=params)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error fetching TMDB data: {e}")
        return None

@app.route('/')
def home():
    """Home page with trending movies and TV shows"""
    # Get trending movies and TV shows
    trending_movies = get_tmdb_data('trending/movie/week')
    trending_tv = get_tmdb_data('trending/tv/week')
    popular_movies = get_tmdb_data('movie/popular')
    popular_tv = get_tmdb_data('tv/popular')
    
    return render_template('home.html', 
                         trending_movies=trending_movies,
                         trending_tv=trending_tv,
                         popular_movies=popular_movies,
                         popular_tv=popular_tv)

@app.route('/movies')
def movies():
    """Movies page with categories"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    movies_data = get_tmdb_data('discover/movie', params)
    genres = get_tmdb_data('genre/movie/list')
    
    return render_template('movies.html', 
                         movies=movies_data,
                         genres=genres,
                         current_genre=genre)

@app.route('/tv')
def tv_shows():
    """TV shows page"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    tv_data = get_tmdb_data('discover/tv', params)
    genres = get_tmdb_data('genre/tv/list')
    
    return render_template('tv.html', 
                         tv_shows=tv_data,
                         genres=genres,
                         current_genre=genre)

@app.route('/watch/movie/<int:movie_id>')
def watch_movie(movie_id):
    """Movie player page"""
    movie_details = get_tmdb_data(f'movie/{movie_id}')
    embed_url = f"{AUTOEMBED_BASE_URL}/movie/{movie_id}"
    
    return render_template('player.html', 
                         content=movie_details,
                         embed_url=embed_url,
                         content_type='movie')

@app.route('/watch/tv/<int:tv_id>/<int:season>/<int:episode>')
def watch_tv(tv_id, season, episode):
    """TV show player page"""
    tv_details = get_tmdb_data(f'tv/{tv_id}')
    embed_url = f"{AUTOEMBED_BASE_URL}/tv/{tv_id}/{season}/{episode}"
    
    return render_template('player.html', 
                         content=tv_details,
                         embed_url=embed_url,
                         content_type='tv',
                         season=season,
                         episode=episode)

@app.route('/api/search')
def search():
    """Search API endpoint"""
    query = request.args.get('q', '')
    if not query:
        return jsonify({'results': []})
    
    search_results = get_tmdb_data('search/multi', {'query': query})
    return jsonify(search_results)

@app.route('/api/movies/load-more')
def load_more_movies():
    """Load more movies for infinite scroll"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    movies_data = get_tmdb_data('discover/movie', params)
    return jsonify(movies_data)

@app.route('/api/tv/load-more')
def load_more_tv():
    """Load more TV shows for infinite scroll"""
    page = request.args.get('page', 1, type=int)
    genre = request.args.get('genre', '')
    
    params = {'page': page}
    if genre:
        params['with_genres'] = genre
    
    tv_data = get_tmdb_data('discover/tv', params)
    return jsonify(tv_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
